@echo off
REM 一键修复Cursor OOM崩溃问题
REM 综合解决方案

echo ========================================
echo Cursor OOM 崩溃问题一键修复
echo 错误代码: -536870904
echo ========================================

echo [步骤1] 停止所有Cursor进程...
taskkill /f /im cursor.exe 2>nul
timeout /t 2 >nul

echo [步骤2] 清理缓存和索引...
if exist "%APPDATA%\Cursor\Cache" (
    rmdir /s /q "%APPDATA%\Cursor\Cache" 2>nul
    echo - 缓存清理完成
)
if exist "%APPDATA%\Cursor\index" (
    rmdir /s /q "%APPDATA%\Cursor\index" 2>nul
    echo - 索引清理完成
)

echo [步骤3] 检查.cursorignore文件...
if exist ".cursorignore" (
    echo - .cursorignore 文件已存在
) else (
    echo - 创建 .cursorignore 文件...
    echo 请运行项目根目录下的脚本来创建.cursorignore文件
)

echo [步骤4] 设置内存限制环境变量...
set NODE_OPTIONS=--max-old-space-size=3072
echo - 内存限制设置为 3GB

echo [步骤5] 提供启动选项...
echo.
echo 请选择启动方式:
echo 1. 标准内存限制启动 (推荐)
echo 2. 安全模式启动 (如果标准模式仍崩溃)
echo 3. 仅清理，不启动
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo 启动标准内存限制模式...
    call scripts\cursor_memory_fix.bat
) else if "%choice%"=="2" (
    echo 启动安全模式...
    call scripts\cursor_safe_mode.bat
) else if "%choice%"=="3" (
    echo 清理完成，请手动启动Cursor
) else (
    echo 无效选择，默认使用标准模式...
    call scripts\cursor_memory_fix.bat
)

echo.
echo ========================================
echo 修复完成！
echo.
echo 后续建议:
echo 1. 定期运行此脚本清理缓存
echo 2. 使用 scripts\cursor_memory_monitor.ps1 监控内存
echo 3. 避免同时打开过多大文件
echo 4. 定期重启Cursor释放内存
echo ========================================
pause
