@echo off
REM Cursor内存优化启动脚本 - 紧急修复版
REM 解决OOM崩溃问题

echo ========================================
echo Cursor 内存优化启动脚本
echo 解决 OOM 崩溃问题 (错误代码: -536870904)
echo ========================================

REM 1. 关闭所有Cursor实例
echo [1/5] 关闭现有Cursor实例...
taskkill /f /im cursor.exe 2>nul
timeout /t 2 >nul

REM 2. 清理缓存
echo [2/5] 清理Cursor缓存...
if exist "%APPDATA%\Cursor\Cache" (
    rmdir /s /q "%APPDATA%\Cursor\Cache" 2>nul
    echo 缓存清理完成
) else (
    echo 缓存目录不存在，跳过清理
)

REM 3. 清理索引
echo [3/5] 清理Cursor索引...
if exist "%APPDATA%\Cursor\index" (
    rmdir /s /q "%APPDATA%\Cursor\index" 2>nul
    echo 索引清理完成
) else (
    echo 索引目录不存在，跳过清理
)

REM 4. 设置内存限制环境变量
echo [4/5] 设置内存限制...
set NODE_OPTIONS=--max-old-space-size=3072

REM 5. 启动Cursor with 内存限制
echo [5/5] 启动Cursor (内存限制: 3GB)...
echo 启动参数: --max-memory=3072 --disable-gpu --no-sandbox

REM 检查Cursor是否存在
where cursor >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 找不到cursor命令，请确保Cursor已正确安装并添加到PATH
    echo 尝试使用完整路径启动...
    if exist "%LOCALAPPDATA%\Programs\cursor\Cursor.exe" (
        start "" "%LOCALAPPDATA%\Programs\cursor\Cursor.exe" --max-memory=3072 --disable-gpu --no-sandbox
    ) else (
        echo 请手动启动Cursor并使用以下参数:
        echo cursor --max-memory=3072 --disable-gpu --no-sandbox
    )
) else (
    cursor --max-memory=3072 --disable-gpu --no-sandbox
)

echo ========================================
echo 启动完成！
echo 如果仍然崩溃，请运行: cursor_safe_mode.bat
echo ========================================
pause
