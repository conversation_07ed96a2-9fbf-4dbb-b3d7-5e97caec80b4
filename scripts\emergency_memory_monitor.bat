@echo off
REM 紧急内存监控 - 简化版本，立即可用

echo ========================================
echo 🔍 Cursor内存实时监控
echo 阈值: 1.8GB (警告) / 2.5GB (危险)
echo ========================================

:monitor_loop
for /f "tokens=2 delims=," %%a in ('tasklist /fi "imagename eq cursor.exe" /fo csv ^| find "cursor.exe"') do (
    set "memory_kb=%%~a"
    set "memory_kb=!memory_kb: =!"
    set "memory_kb=!memory_kb:,=!"
    set "memory_kb=!memory_kb:K=!"
    
    set /a memory_mb=!memory_kb!/1024
    
    echo [%time%] Cursor内存使用: !memory_mb! MB
    
    if !memory_mb! GTR 1800 (
        echo ⚠️  警告: 内存使用超过1.8GB
        if !memory_mb! GTR 2500 (
            echo 🚨 危险: 内存使用超过2.5GB，建议立即重启Cursor
            echo 按任意键停止监控并重启Cursor...
            pause >nul
            goto restart_cursor
        )
    )
)

timeout /t 10 >nul
goto monitor_loop

:restart_cursor
echo 重启Cursor...
taskkill /f /im cursor.exe
timeout /t 3 >nul
call emergency_cursor_fix.bat
exit

:no_cursor
echo Cursor未运行，等待启动...
timeout /t 5 >nul
goto monitor_loop
