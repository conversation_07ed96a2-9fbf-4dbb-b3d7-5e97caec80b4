# Cursor内存监控脚本
# 实时监控Cursor内存使用，防止OOM崩溃

param(
    [int]$ThresholdMB = 3000,  # 内存阈值 (MB)
    [int]$CheckIntervalSeconds = 30  # 检查间隔 (秒)
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Cursor 内存监控脚本启动" -ForegroundColor Green
Write-Host "内存阈值: $ThresholdMB MB" -ForegroundColor Yellow
Write-Host "检查间隔: $CheckIntervalSeconds 秒" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

$logFile = "scripts\cursor_memory_monitor.log"

function Write-Log {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] $Message"
    Write-Host $logEntry
    Add-Content -Path $logFile -Value $logEntry
}

function Get-CursorMemoryUsage {
    try {
        $cursorProcesses = Get-Process -Name "cursor" -ErrorAction SilentlyContinue
        if ($cursorProcesses) {
            $totalMemoryMB = ($cursorProcesses | Measure-Object WorkingSet64 -Sum).Sum / 1MB
            return [math]::Round($totalMemoryMB, 2)
        }
        return 0
    }
    catch {
        return 0
    }
}

function Clear-CursorCache {
    Write-Log "执行缓存清理..."
    try {
        $cachePath = "$env:APPDATA\Cursor\Cache"
        if (Test-Path $cachePath) {
            Remove-Item -Path $cachePath -Recurse -Force -ErrorAction SilentlyContinue
            Write-Log "缓存清理完成"
        }
    }
    catch {
        Write-Log "缓存清理失败: $($_.Exception.Message)"
    }
}

function Send-MemoryAlert {
    param($MemoryUsage)
    Write-Host "⚠️  内存警告: Cursor使用了 $MemoryUsage MB 内存!" -ForegroundColor Red
    Write-Log "内存警告: $MemoryUsage MB (阈值: $ThresholdMB MB)"
    
    # 可以在这里添加更多警告机制，如发送邮件、弹窗等
    [System.Windows.Forms.MessageBox]::Show(
        "Cursor内存使用过高: $MemoryUsage MB`n建议保存工作并重启Cursor",
        "内存警告",
        [System.Windows.Forms.MessageBoxButtons]::OK,
        [System.Windows.Forms.MessageBoxIcon]::Warning
    ) | Out-Null
}

# 加载Windows Forms用于弹窗
Add-Type -AssemblyName System.Windows.Forms

Write-Log "内存监控开始运行..."

while ($true) {
    $memoryUsage = Get-CursorMemoryUsage
    
    if ($memoryUsage -gt 0) {
        Write-Log "Cursor内存使用: $memoryUsage MB"
        
        if ($memoryUsage -gt $ThresholdMB) {
            Send-MemoryAlert -MemoryUsage $memoryUsage
            
            # 如果内存使用超过阈值的150%，自动清理缓存
            if ($memoryUsage -gt ($ThresholdMB * 1.5)) {
                Clear-CursorCache
            }
        }
    } else {
        Write-Log "Cursor未运行"
    }
    
    Start-Sleep -Seconds $CheckIntervalSeconds
}
