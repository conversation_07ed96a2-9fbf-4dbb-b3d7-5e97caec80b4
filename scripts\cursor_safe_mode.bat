@echo off
REM Cursor安全模式启动脚本
REM 当正常启动仍然崩溃时使用

echo ========================================
echo Cursor 安全模式启动
echo 禁用所有扩展和高级功能
echo ========================================

REM 关闭所有Cursor实例
echo [1/3] 关闭现有Cursor实例...
taskkill /f /im cursor.exe 2>nul
timeout /t 3 >nul

REM 设置最严格的内存限制
echo [2/3] 设置严格内存限制...
set NODE_OPTIONS=--max-old-space-size=2048

REM 启动安全模式
echo [3/3] 启动Cursor安全模式...
echo 启动参数: --safe-mode --max-memory=2048 --disable-gpu --no-sandbox

where cursor >nul 2>&1
if %errorlevel% neq 0 (
    if exist "%LOCALAPPDATA%\Programs\cursor\Cursor.exe" (
        start "" "%LOCALAPPDATA%\Programs\cursor\Cursor.exe" --safe-mode --max-memory=2048 --disable-gpu --no-sandbox
    ) else (
        echo 请手动启动Cursor并使用以下参数:
        echo cursor --safe-mode --max-memory=2048 --disable-gpu --no-sandbox
    )
) else (
    cursor --safe-mode --max-memory=2048 --disable-gpu --no-sandbox
)

echo ========================================
echo 安全模式启动完成！
echo 在安全模式下，所有扩展都被禁用
echo 请逐步重新启用必要的扩展
echo ========================================
pause
