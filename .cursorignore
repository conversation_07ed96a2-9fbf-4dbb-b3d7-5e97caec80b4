# Cursor索引排除文件 - 针对fucai3d项目优化
# 减少内存占用，防止OOM崩溃

# ===== 通用排除 =====
node_modules/
.git/
.svn/
.hg/
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db

# ===== Python相关 =====
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===== 虚拟环境 =====
venv/
env/
ENV/
.venv/
.env/

# ===== 数据库文件 (大文件) =====
*.db
*.sqlite
*.sqlite3
data/*.db
cache/*.db
backups/**/*.db
backup_*/**/*.db

# ===== 日志文件 =====
logs/
*.log
*.log.*
app.log
deployment.log

# ===== 缓存目录 =====
cache/
.cache/
temp/
.temp/

# ===== 备份文件 =====
backups/
backup_*/
*.backup
*.bak

# ===== 模型文件 (大文件) =====
models/**/*.pkl
models/**/*.joblib
models/**/*.h5
models/**/*.model
*.pkl
*.joblib

# ===== 前端构建文件 =====
web-frontend/node_modules/
web-frontend/dist/
web-frontend/build/
web-frontend/.next/
web-frontend/.nuxt/

# ===== 报告和输出文件 =====
reports/
output/
analysis_output/
*.json
*.csv
*.xlsx

# ===== 调试文件 =====
debug/snapshots/
debug/reports/
debug/issues/
debug/analysis/

# ===== 文档目录 (减少索引负担) =====
docs/
文档/
福彩3d预测项目开发指南参考/
项目管理文档/

# ===== 测试文件 =====
tests/
test_*.py
*_test.py

# ===== 配置备份 =====
*.yaml.backup
config_backups/

# ===== 大型数据文件 =====
*.zip
*.tar.gz
*.rar
*.7z

# ===== 但保留重要文件 =====
!src/
!config/*.yaml
!requirements.txt
!README.md
!start_system.py
