# Cursor 内存不足 (oom) 崩溃问题的全面解决方案

你的 Cursor 编辑器频繁因 "oom"(内存不足) 错误崩溃，错误代码为 - 536870904。我完全理解你不想升级硬件的需求，以下将提供一套系统的解决方案，通过软件设置调整和使用习惯优化来解决崩溃问题。

## 一、理解 "oom" 崩溃的本质

### 1.1 错误代码解析

错误代码 - 536870904 对应的十六进制是 0xE0000008，这是 Windows 系统中与内存不足相关的异常代码[(6)](https://wenku.csdn.net/answer/2183vqcbrg)。当程序申请的内存超过系统或进程允许的最大限制时，系统会强制终止该进程以防止系统崩溃[(6)](https://wenku.csdn.net/answer/2183vqcbrg)。

### 1.2 Cursor 内存使用特点

Cursor 编辑器基于 Electron 框架构建，与 VSCode 有相似的内存管理机制[(7)](https://wenku.csdn.net/answer/3z7ybjva1i)。它有以下几个内存使用特点：



1.  **索引内存占用**：Cursor 会对代码库进行索引，生成嵌入向量，这一过程会消耗大量内存，尤其是在处理大型项目时[(38)](https://learn-cursor.com/en/docs/context/codebase-indexing)。

2.  **扩展内存消耗**：第三方扩展可能引入额外的内存开销，特别是功能复杂的扩展[(7)](https://wenku.csdn.net/answer/3z7ybjva1i)。

3.  **AI 功能内存需求**：Cursor 的 AI 功能需要加载大型语言模型，这些模型本身就占用大量内存[(29)](https://anysphere.mintlify.app/context/max-mode)。

4.  **缓存机制**：Cursor 会缓存大量数据以提高性能，但缓存管理不当会导致内存泄漏[(2)](https://github.com/cursor/cursor/issues/2296)。

## 二、紧急恢复策略

### 2.1 安全模式启动

当 Cursor 频繁崩溃无法正常使用时，可以通过安全模式启动：



1.  关闭所有 Cursor 实例

2.  打开命令提示符 (CMD) 或 PowerShell

3.  输入以下命令启动 Cursor：



```
cursor --safe-mode
```

安全模式会禁用所有扩展并限制某些功能，有助于稳定启动[(2)](https://github.com/cursor/cursor/issues/2296)。

### 2.2 强制内存限制启动

使用以下命令启动 Cursor 并设置最大内存限制：



```
cursor --max-memory=3072
```

这会将 Cursor 的最大内存使用限制为 3GB (3072MB)，低于默认设置，减少内存不足的风险[(2)](https://github.com/cursor/cursor/issues/2296)。

### 2.3 项目拆分策略

如果某个大型项目导致频繁崩溃，可以尝试：



1.  将大型项目按功能模块拆分为多个小项目

2.  只打开当前需要编辑的模块

3.  使用`@codebase`功能在不同模块间进行上下文切换[(38)](https://learn-cursor.com/en/docs/context/codebase-indexing)

## 三、系统级内存优化

### 3.1 调整虚拟内存设置

适当增加虚拟内存可以为 Cursor 提供更多的内存空间：



1.  右键点击 "此电脑"，选择 "属性"

2.  点击 "高级系统设置"

3.  在 "高级" 选项卡下，点击 "性能" 区域的 "设置"

4.  在 "高级" 选项卡下，点击 "虚拟内存" 区域的 "更改"

5.  取消 "自动管理所有驱动器的分页文件大小" 勾选

6.  选择一个有足够空间的驱动器 (推荐 SSD)

7.  选择 "自定义大小"，设置初始大小为 24576MB (24GB)，最大值为 32768MB (32GB)

8.  点击 "设置"，然后 "确定" 保存设置[(2)](https://github.com/cursor/cursor/issues/2296)

### 3.2 优化 Windows 内存管理

调整 Windows 系统的内存管理设置：



1.  打开注册表编辑器 (regedit)

2.  导航到`HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Memory Management`

3.  找到或创建以下 DWORD 值：

*   **DisablePagingExecutive**：设置为 1 可以将核心系统驱动程序和数据保留在物理内存中

*   **LargeSystemCache**：设置为 1 可以优化文件系统缓存

1.  重启计算机使设置生效[(106)](https://learn.microsoft.com/en-us/windows/win32/extensible-storage-engine/resource-parameters)

### 3.3 关闭不必要的后台程序

在使用 Cursor 时，关闭其他占用大量内存的程序：



1.  按`Ctrl+Shift+Esc`打开任务管理器

2.  在 "进程" 选项卡中，结束不使用的程序

3.  特别注意浏览器、虚拟机、设计软件等高内存消耗程序

## 四、Cursor 核心设置优化

### 4.1 内存限制参数优化

通过命令行参数可以精确控制 Cursor 的内存使用：



1.  **基本内存限制**：



```
cursor --max-memory=4096
```

这会将 Cursor 的最大内存使用限制为 4GB，这是在 16GB 物理内存系统上的合理设置[(2)](https://github.com/cursor/cursor/issues/2296)。



1.  **Node.js 堆内存限制**：



```
set NODE\_OPTIONS=--max-old-space-size=4096

cursor
```

这会限制 Node.js 环境的堆内存大小为 4GB[(2)](https://github.com/cursor/cursor/issues/2296)。



1.  **GPU 加速优化**：



```
cursor --disable-gpu
```

禁用 GPU 加速可能会解决某些渲染相关的内存问题[(7)](https://wenku.csdn.net/answer/3z7ybjva1i)。



1.  **完整优化启动参数组合**：



```
cursor --max-memory=4096 --disable-gpu --no-sandbox
```

这组参数可以在测试时使用，以确定是否是 GPU 加速或沙盒机制导致的问题[(2)](https://github.com/cursor/cursor/issues/2296)。

### 4.2 索引设置优化

Cursor 的代码库索引是内存占用的主要来源之一，优化索引设置可以显著减少内存消耗：



1.  **创建.cursorindexingignore 文件**：

    在项目根目录创建`.cursorindexingignore`文件，添加要排除的文件或目录模式：



```
\# 通用排除

node\_modules/

dist/

build/

out/

\*.min.js

\*.bundle.js

\*.log

.git/

.svn/

.hg/

\# 语言特定排除

\_\_pycache\_\_/

\*.pyc

\*.pyo

\*.class

\*.jar

vendor/

.venv/

env/

\*.dll

\*.exe
```

这将防止 Cursor 索引这些文件，从而减少内存使用[(21)](https://forum.cursor.sh/t/codebase-indexing-too-many-files-to-upload/86310)。



1.  **调整索引行为**：

*   打开 Cursor 设置 (Ctrl+,)

*   搜索 "indexing"

*   禁用 "Index new folders by default" 选项

*   降低 "Maximum number of files to index" 值，默认是 10000[(37)](https://docs.cursor.com/context/codebase-indexing)

1.  **重建索引**：



```
a. 关闭Cursor

b. 删除索引目录（Windows路径：%APPDATA%\Cursor\index）

c. 重启Cursor并让其重新构建索引（只索引必要文件）
```

这有助于解决可能的索引损坏问题[(2)](https://github.com/cursor/cursor/issues/2296)。

### 4.3 缓存管理优化

Cursor 的缓存机制可能导致内存泄漏，优化缓存设置可以改善这一问题：



1.  **调整缓存大小限制**：

*   打开设置，搜索 "cache"

*   将 "Cache Size Limit" 设置为 128MB（默认 512MB 的四分之一）

*   启用 "Auto-clear cache when limit is reached" 选项[(2)](https://github.com/cursor/cursor/issues/2296)

1.  **定期手动清理缓存**：



```
a. 关闭Cursor

b. 删除缓存目录（Windows路径：%APPDATA%\Cursor\Cache）

c. 重启Cursor
```

建议每周至少进行一次这样的操作[(2)](https://github.com/cursor/cursor/issues/2296)。



1.  **配置文件优化缓存策略**：

    合理配置缓存机制，可以加快代码分析和补全速度[(2)](https://github.com/cursor/cursor/issues/2296)。

*   打开用户设置 (Ctrl+,)

*   点击 "Edit in settings.json"

*   添加以下配置：



```
{

&#x20; "enableIncrementalCache": true, // 启用增量缓存

&#x20; "useMemoryCache": true // 使用内存缓存

}
```

## 五、扩展管理优化

扩展是导致 Cursor 内存问题的常见原因，以下是扩展管理的优化策略：

### 5.1 识别和禁用高内存扩展



1.  **进程资源分析**：

*   按`Ctrl+Shift+P`打开命令面板

*   输入`Developer: Open Process Explorer`，查看各进程的资源占用情况

*   识别哪些扩展占用了大量内存[(3)](https://github.com/getcursor/cursor/issues/1009)

1.  **分阶段禁用扩展**：

*   按`Ctrl+Shift+P`打开命令面板

*   输入`Extensions: Show Installed Extensions`

*   逐个禁用扩展，测试 Cursor 的内存使用情况

*   特别注意以下类型的扩展：


    *   代码分析类扩展

    *   版本控制系统扩展

    *   大型语言服务扩展[(7)](https://wenku.csdn.net/answer/3z7ybjva1i)

1.  **使用安全模式诊断**：



```
cursor --safe-mode
```

这将禁用所有扩展启动 Cursor，有助于确定是否是扩展引起的内存问题[(2)](https://github.com/cursor/cursor/issues/2296)。

### 5.2 扩展使用最佳实践



1.  **仅安装必要扩展**：

    只保留当前项目真正需要的扩展，卸载其他所有扩展[(75)](https://www.cursor.fan/tutorial/HowTo/managing-extensions-in-cursor/)。

2.  **扩展更新管理**：

*   禁用自动更新：



```
{

&#x20; "extensions.autoUpdate": false

}
```



*   手动更新扩展，避免在工作时进行更新操作[(76)](https://support.mozilla.org/en-US/kb/disable-or-remove-add-ons#w_removing-extensions)。

1.  **扩展配置优化**：

    对于必须使用的扩展，调整其内存相关设置：

*   降低代码分析频率

*   禁用实时预览功能

*   限制代码检查范围[(78)](https://marketplace.visualstudio.com/items?itemName=muzaisimao.vscode-disable-extensions)

## 六、代码库索引优化

### 6.1 智能排除策略

通过精细控制索引范围，可以显著减少内存使用：



1.  **增强版.cursorignore 配置**：

    在项目根目录创建`.cursorignore`文件，添加更精细的排除规则：



```
\# 排除所有node\_modules目录

node\_modules/

\# 排除所有日志文件

\*.log

\# 排除构建输出目录

build/

dist/

out/

\# 排除特定类型的文件

\*.min.js

\*.bundle.js

\*.map

\# 排除数据库文件

\*.db

\*.sqlite

\# 但包含特定文件

!important-data.csv
```

注意：`.cursorignore`的优先级高于`.gitignore`[(22)](https://docs.cursor.com/context/ignore-files)。



1.  **项目级索引配置**：

*   打开项目设置

*   找到 "Codebase Indexing" 部分

*   禁用 "Index new folders by default" 选项

*   手动选择需要索引的目录[(23)](https://anysphere.mintlify.app/context/codebase-indexing)

1.  **使用.gitignore 进行索引控制**：

    可以直接使用现有的`.gitignore`文件来控制索引行为，Cursor 会自动识别并应用这些规则[(23)](https://anysphere.mintlify.app/context/codebase-indexing)。

### 6.2 索引行为优化

调整索引行为可以减少内存峰值：



1.  **延迟索引加载**：

*   打开设置

*   搜索 "performance.delaymodelloading"

*   启用该选项，它会延迟模型加载直到真正需要时[(2)](https://github.com/cursor/cursor/issues/2296)。

1.  **分阶段索引大型项目**：

*   将大型项目分解为多个子项目

*   分别为每个子项目创建索引

*   只在需要时加载特定子项目的索引[(21)](https://forum.cursor.sh/t/codebase-indexing-too-many-files-to-upload/86310)

1.  **手动索引控制**：

*   使用`@codebase`命令手动选择需要索引的文件

*   避免自动索引整个项目[(38)](https://learn-cursor.com/en/docs/context/codebase-indexing)

## 七、高级内存管理技巧

### 7.1 内存监控与分析

通过监控 Cursor 的内存使用情况，可以更好地识别问题所在：



1.  **内置内存监控**：

*   按`Ctrl+Shift+P`打开命令面板

*   输入`Developer: Toggle Memory Info`

*   观察内存使用情况，特别是峰值出现的时间和操作[(7)](https://wenku.csdn.net/answer/3z7ybjva1i)

1.  **生成内存转储文件**：

*   当 Cursor 崩溃时，检查崩溃日志目录（% APPDATA%\Cursor\logs）

*   查找.dmp 文件，这些是内存转储文件

*   使用调试工具（如 WinDbg）分析这些文件，确定内存泄漏点[(8)](https://ask.csdn.net/questions/8301846)

1.  **性能分析工具**：

*   使用 Windows 性能监视器监控 Cursor 进程的内存使用

*   设置内存使用阈值警报，及时发现异常增长[(8)](https://ask.csdn.net/questions/8301846)

### 7.2 高级配置文件优化

对于高级用户，可以直接编辑配置文件进行更精细的优化：



1.  **用户设置优化**：

    注意：这个配置文件要放在 Cursor 安装目录的 config 文件夹下[(2)](https://github.com/cursor/cursor/issues/2296)。

*   打开用户设置 (Ctrl+,)

*   点击 "Edit in settings.json"

*   添加以下配置（根据需要调整）：



```
{

&#x20; "editor.preloadmodels": false,

&#x20; "performance.delaymodelloading": true,

&#x20; "memory.maxheapsize": "2048" // 设置最大堆内存为2GB

}
```



1.  **针对特定语言的优化**：

    对于 Java 开发，可以添加以下配置：



```
{

&#x20; "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx16G -Xms2G -Xlog:disable"

&#x20; }
```

这是针对 Java 开发的内存设置优化[(2)](https://github.com/cursor/cursor/issues/2296)。



1.  **自定义内存分配策略**：

    对于内存紧张的系统，可以尝试以下配置：



```
{

&#x20; "memory.initialheapsize": "1024", // 初始堆大小为1GB

&#x20; "memory.maxheapsize": "3072", // 最大堆大小为3GB

&#x20; "memory.gcinterval": "60000" // 每60秒执行一次垃圾回收

}
```

注意：这些设置需要根据系统实际情况进行调整[(2)](https://github.com/cursor/cursor/issues/2296)。

## 八、工作流程和使用习惯优化

### 8.1 项目管理策略

优化项目管理方式可以减少内存压力：



1.  **项目拆分策略**：

*   将大型项目拆分为多个小型子项目

*   使用工作区文件来管理多个子项目

*   只打开当前工作所需的子项目[(9)](https://cloud.tencent.com/developer/ask/sof/106817367/answer/117161864)

1.  **大文件处理技巧**：

*   避免在 Cursor 中直接编辑超过 10MB 的大文件

*   使用专门的大文件编辑器处理大型文本文件

*   对于必须处理的大文件，启用只读模式[(9)](https://cloud.tencent.com/developer/ask/sof/106817367/answer/117161864)

1.  **版本控制系统优化**：

*   避免在大型项目中启用 Git 历史索引

*   限制 Git 日志的显示行数

*   定期清理 Git 历史记录[(180)](https://www.jetbrains.com/help/pycharm/version-control-tool-window-history-tab.html)

### 8.2 日常使用习惯优化

调整日常使用习惯可以显著减少内存问题：



1.  **关闭不使用的编辑器窗口**：

*   只保留当前工作所需的编辑器窗口

*   使用`Ctrl+W`关闭不使用的文件

*   定期重启 Cursor 以释放内存[(7)](https://wenku.csdn.net/answer/3z7ybjva1i)

1.  **批量操作而非实时操作**：

*   避免频繁的小修改和保存

*   集中进行代码修改，然后一次性保存

*   批量重命名和重构操作[(181)](https://www.builder.io/blog/cursor-tips)

1.  **内存清理习惯**：

*   定期使用`Ctrl+Shift+P`打开命令面板，输入`Free Memory`执行内存清理

*   在长时间工作会话中，每 1-2 小时重启一次 Cursor

*   在进行大型操作前，手动清理内存[(2)](https://github.com/cursor/cursor/issues/2296)

## 九、特殊场景优化

### 9.1 大型代码库处理策略

对于特别大型的代码库，需要采取特殊的优化策略：



1.  **分层次索引策略**：

    这样可以精细控制哪些代码被索引[(22)](https://docs.cursor.com/context/ignore-files)。

*   创建多个`.cursorignore`文件，按层次控制索引

*   在项目根目录创建主`.cursorignore`

*   在子目录中创建特定的`.cursorignore`文件

*   例如：



```
\# 根目录.cursorignore

node\_modules/

!core-module/

\# core-module/.cursorignore

tests/
```



1.  **选择性索引关键目录**：

*   只索引项目中的关键目录

*   例如，只索引 src 目录，而排除 tests、docs 等目录

*   可以通过项目设置中的 "Codebase Indexing" 部分进行配置[(23)](https://anysphere.mintlify.app/context/codebase-indexing)

1.  **使用符号链接管理大型依赖**：

*   将大型依赖库存储在项目外部

*   使用符号链接将其链接到项目中

*   在`.cursorignore`中排除符号链接指向的实际目录[(139)](https://github.com/prettier/prettier/issues/1358)

### 9.2 特定语言开发优化

不同编程语言的开发可能需要特定的优化措施：



1.  **JavaScript/TypeScript 项目优化**：

*   排除 node\_modules 目录

*   禁用 JavaScript 语言服务的某些功能

*   限制类型检查的范围

1.  **Python 项目优化**：

*   排除虚拟环境目录

*   限制 Python 语言服务器的内存使用

*   禁用不必要的代码分析功能[(116)](https://github.com/pyscf/pyscf/issues/470)

1.  **Java 项目优化**：

*   配置 Java 语言服务器的内存参数

*   排除构建目录和依赖库

*   禁用代码导航功能中的某些高级特性[(11)](https://blog.51cto.com/u_15500998/14050562)

## 十、监控与维护计划

### 10.1 内存使用监控系统

建立有效的内存使用监控系统可以帮助预防崩溃：



1.  **设置内存使用阈值**：

*   按`Ctrl+Shift+Esc`打开任务管理器

*   在 "性能" 选项卡中，观察内存使用情况

*   设置一个阈值（如 12GB，对于 16GB 系统），当接近这个阈值时，关闭 Cursor 或其他程序[(7)](https://wenku.csdn.net/answer/3z7ybjva1i)

1.  **自动内存监控脚本**：

    创建一个简单的 PowerShell 脚本，定期监控 Cursor 的内存使用：



```
while (\$true) {

&#x20; \$cursor = Get-Process -Name cursor -ErrorAction SilentlyContinue

&#x20; if (\$cursor) {

&#x20;   \$mem = \$cursor.WorkingSet64 / 1MB

&#x20;   if (\$mem -gt 3000) { # 超过3GB

&#x20;     Write-Host "Cursor使用了\$mem MB内存，接近阈值！"

&#x20;     \# 可以添加自动保存和重启Cursor的代码

&#x20;   }

&#x20; }

&#x20; Start-Sleep -Seconds 60 # 每分钟检查一次

}
```

这个脚本可以在后台运行，监控 Cursor 的内存使用情况[(104)](https://www.controlup.com/script-library-posts/cap-memory-of-process/)。



1.  **性能日志记录**：

*   启用 Windows 性能日志和警报

*   创建自定义数据收集器集

*   定期分析性能日志，识别内存使用模式[(8)](https://ask.csdn.net/questions/8301846)

### 10.2 定期维护计划

建立定期维护计划可以保持 Cursor 处于最佳状态：



1.  **每周维护任务**：

*   清理 Cursor 缓存

*   检查并更新扩展

*   审查已安装的扩展，卸载不再使用的扩展[(2)](https://github.com/cursor/cursor/issues/2296)

1.  **每月维护任务**：

*   重建代码库索引

*   检查系统更新和驱动程序更新

*   分析内存使用模式，识别潜在问题[(2)](https://github.com/cursor/cursor/issues/2296)

1.  **季度维护任务**：

*   备份并重置 Cursor 设置

*   检查系统资源使用情况

*   评估硬件配置是否仍然满足工作需求[(2)](https://github.com/cursor/cursor/issues/2296)

## 十一、综合优化方案实施路线图

根据问题严重程度和实施难度，建议按照以下路线图实施优化措施：

### 11.1 紧急修复阶段（立即实施）



1.  **设置内存限制**：

    使用`cursor --max-memory=4096`命令启动 Cursor，将内存使用限制在 4GB 以内[(2)](https://github.com/cursor/cursor/issues/2296)。

2.  **清理缓存**：

    删除缓存目录（% APPDATA%\Cursor\Cache）以释放内存[(2)](https://github.com/cursor/cursor/issues/2296)。

3.  **禁用非必要扩展**：

    按`Ctrl+Shift+P`打开命令面板，输入`Extensions: Show Installed Extensions`，禁用所有非必要扩展[(75)](https://www.cursor.fan/tutorial/HowTo/managing-extensions-in-cursor/)。

### 11.2 基础优化阶段（1-2 天内完成）



1.  **创建.cursorignore 文件**：

    在项目根目录创建`.cursorignore`文件，添加排除规则[(22)](https://docs.cursor.com/context/ignore-files)。

2.  **调整缓存设置**：

    将缓存大小限制设置为 128MB，并启用自动清理功能[(2)](https://github.com/cursor/cursor/issues/2296)。

3.  **优化索引设置**：

    禁用 "Index new folders by default" 选项，手动选择需要索引的目录[(23)](https://anysphere.mintlify.app/context/codebase-indexing)。

### 11.3 深度优化阶段（1 周内完成）



1.  **配置文件优化**：

    编辑 settings.json 文件，添加内存相关优化配置[(2)](https://github.com/cursor/cursor/issues/2296)。

2.  **扩展管理**：

    彻底审查所有扩展，卸载不必要的扩展，优化必要扩展的设置[(78)](https://marketplace.visualstudio.com/items?itemName=muzaisimao.vscode-disable-extensions)。

3.  **系统内存设置调整**：

    调整虚拟内存设置，优化 Windows 内存管理[(106)](https://learn.microsoft.com/en-us/windows/win32/extensible-storage-engine/resource-parameters)。

### 11.4 长期维护阶段（持续进行）



1.  **建立监控系统**：

    设置内存使用监控，创建自动维护脚本[(104)](https://www.controlup.com/script-library-posts/cap-memory-of-process/)。

2.  **定期维护计划**：

    建立每周、每月和季度的维护计划并严格执行[(2)](https://github.com/cursor/cursor/issues/2296)。

3.  **工作流程优化**：

    持续优化项目管理方式和日常使用习惯[(181)](https://www.builder.io/blog/cursor-tips)。

## 十二、总结与关键要点

通过以上全面的优化方案，你应该能够显著减少 Cursor 因内存不足导致的崩溃问题。以下是关键要点：



1.  **内存限制是关键**：使用`--max-memory`参数将 Cursor 的内存使用限制在合理范围内，对于 16GB 系统，4GB 是一个合适的初始设置[(2)](https://github.com/cursor/cursor/issues/2296)。

2.  **索引优化是核心**：通过`.cursorignore`文件和索引设置，精细控制索引范围，可以显著减少内存消耗[(22)](https://docs.cursor.com/context/ignore-files)。

3.  **扩展管理不可忽视**：扩展是导致内存问题的常见原因，需要定期审查和优化[(75)](https://www.cursor.fan/tutorial/HowTo/managing-extensions-in-cursor/)。

4.  **系统级优化同样重要**：调整虚拟内存设置，优化 Windows 内存管理，可以为 Cursor 提供更好的运行环境[(106)](https://learn.microsoft.com/en-us/windows/win32/extensible-storage-engine/resource-parameters)。

5.  **工作流程和使用习惯影响重大**：优化项目管理方式和日常使用习惯，可以预防许多内存问题[(9)](https://cloud.tencent.com/developer/ask/sof/106817367/answer/117161864)。

通过综合应用这些策略，你应该能够在不升级硬件的情况下，解决 Cursor 因内存不足导致的崩溃问题，恢复高效的开发体验。如果在实施过程中遇到任何问题，请随时咨询技术支持或社区资源。

**参考资料 **

\[1] Common Issues[ https://docs.cursor.com/troubleshooting/common-issues](https://docs.cursor.com/troubleshooting/common-issues)

\[2] The window is not responding when open cursor,  #2296[ https://github.com/cursor/cursor/issues/2296](https://github.com/cursor/cursor/issues/2296)

\[3] High Memory Consumption Leading to Process Being Killed by OOM Killer #1009[ https://github.com/getcursor/cursor/issues/1009](https://github.com/getcursor/cursor/issues/1009)

\[4] Error Message In Cursor Constructor.[ https://community.custom-cursor.com/t/error-message-in-cursor-constructor/228263](https://community.custom-cursor.com/t/error-message-in-cursor-constructor/228263)

\[5] cursor窗口意外终止(原因:"oom"，代码:"-536870904") - CSDN文库[ https://wenku.csdn.net/answer/50assu2thc](https://wenku.csdn.net/answer/50assu2thc)

\[6] 窗口意外终止(原因:"oom"，代码:-536870904" - CSDN文库[ https://wenku.csdn.net/answer/2183vqcbrg](https://wenku.csdn.net/answer/2183vqcbrg)

\[7] vscode窗口意外终止(原因:"oom"，代码:"-536870904" - CSDN文库[ https://wenku.csdn.net/answer/3z7ybjva1i](https://wenku.csdn.net/answer/3z7ybjva1i)

\[8] 窗口意外终止(原因:"OOM","-536870904")，如何定位和解决内存溢出问题?\_编程语言-CSDN问答[ https://ask.csdn.net/questions/8301846](https://ask.csdn.net/questions/8301846)

\[9] VScode崩溃(原因:“oom”，代码:'-536870904')-腾讯云开发者社区-腾讯云[ https://cloud.tencent.com/developer/ask/sof/106817367/answer/117161864](https://cloud.tencent.com/developer/ask/sof/106817367/answer/117161864)

\[10] 当 VSCode 显示窗口意外终止，错误信息为 “OOM”(Out of Memory，内存不足)且代码为 “-536870904” 时，怎么解决 - CSDN文库[ https://wenku.csdn.net/answer/4xqt6e71uz](https://wenku.csdn.net/answer/4xqt6e71uz)

\[11] JDBC游标读不生效导致OOM问题排查分析\_GreatSQL社区的技术博客\_51CTO博客[ https://blog.51cto.com/u\_15500998/14050562](https://blog.51cto.com/u_15500998/14050562)

\[12] 25 Troubleshooting Open Cursor Issues[ https://docs.oracle.com/cd/E40329\_01/admin.1112/e27149/cursor.htm](https://docs.oracle.com/cd/E40329_01/admin.1112/e27149/cursor.htm)

\[13] PSA: Monterey app memory leak isn’t just caused by an accessibility feature[ https://9to5mac.com/2021/11/15/monterey-app-memory-leak-solution-suggestion/](https://9to5mac.com/2021/11/15/monterey-app-memory-leak-solution-suggestion/)

\[14] Client and server cursors - using MySQL[ https://wiki.genexus.com/commwiki/wiki?4188,Client+and+server+cursors+-+using+MySQL](https://wiki.genexus.com/commwiki/wiki?4188,Client+and+server+cursors+-+using+MySQL)

\[15] Show Choices Cursor Memory[ https://forums.rpgmakerweb.com/index.php?threads/show-choices-cursor-memory.153841/](https://forums.rpgmakerweb.com/index.php?threads/show-choices-cursor-memory.153841/)

\[16] Tuning PostgreSQL settings for performance[ https://bun.uptrace.dev/postgres/performance-tuning.html](https://bun.uptrace.dev/postgres/performance-tuning.html)

\[17] forsonny/Enhanced-Cursor-Memory-Bank-System[ https://github.com/forsonny/Enhanced-Cursor-Memory-Bank-System](https://github.com/forsonny/Enhanced-Cursor-Memory-Bank-System)

\[18] Hide Cursor[ https://github.com/alex391/hide-cursor](https://github.com/alex391/hide-cursor)

\[19] davidding/mouse-block[ https://github.com/davidding/mouse-block](https://github.com/davidding/mouse-block)

\[20] Crosshairify - Changes Cursor[ https://addons.mozilla.org/en-US/firefox/addon/crosshairify-changes-cursor/](https://addons.mozilla.org/en-US/firefox/addon/crosshairify-changes-cursor/)

\[21] Codebase Indexing: Too many files to upload[ https://forum.cursor.sh/t/codebase-indexing-too-many-files-to-upload/86310](https://forum.cursor.sh/t/codebase-indexing-too-many-files-to-upload/86310)

\[22] Ignore files[ https://docs.cursor.com/context/ignore-files](https://docs.cursor.com/context/ignore-files)

\[23] Codebase Indexing[ https://anysphere.mintlify.app/context/codebase-indexing](https://anysphere.mintlify.app/context/codebase-indexing)

\[24] .cursorignore and .gitignore are not always respected for Codebase Indexing #965[ https://github.com/getcursor/cursor/issues/965](https://github.com/getcursor/cursor/issues/965)

\[25] Cursor Best Practices[ https://github.com/digitalchild/cursor-best-practices](https://github.com/digitalchild/cursor-best-practices)

\[26] Codebase Indexing No Longer Working Inside Dev Containers #1961[ https://github.com/getcursor/cursor/issues/1961](https://github.com/getcursor/cursor/issues/1961)

\[27] cursor.batchSize()[ https://www.mongodb.com/docs/manual/reference/method/cursor.batchSize/](https://www.mongodb.com/docs/manual/reference/method/cursor.batchSize/)

\[28] Cursor with Sort forEach exceeds maximum RAM, with Index[ https://www.mongodb.com/community/forums/t/cursor-with-sort-foreach-exceeds-maximum-ram-with-index/7461](https://www.mongodb.com/community/forums/t/cursor-with-sort-foreach-exceeds-maximum-ram-with-index/7461)

\[29] Max Mode[ https://anysphere.mintlify.app/context/max-mode](https://anysphere.mintlify.app/context/max-mode)

\[30] Neither allowDiskUse nor cursor are not working as expected with mongoose version 5.2.4 #7328[ https://github.com/Automattic/mongoose/issues/7328](https://github.com/Automattic/mongoose/issues/7328)

\[31] Erorr ORA-1000, maximum open cursors exceeded. - Ask TOM[ https://asktom.oracle.com/ords/f?p=100:11:0::::P11\_QUESTION\_ID:163212348068](https://asktom.oracle.com/ords/f?p=100:11:0::::P11_QUESTION_ID:163212348068)

\[32] Hide Cursor[ https://addons.mozilla.org/en-US/firefox/addon/hide-cursor/](https://addons.mozilla.org/en-US/firefox/addon/hide-cursor/)

\[33] Review of "Hide Cursor" version 1[ https://extensions.gnome.org/review/50575](https://extensions.gnome.org/review/50575)

\[34] burdukowsky/hide-my-figma-cursor-extension[ https://github.com/burdukowsky/hide-my-figma-cursor-extension](https://github.com/burdukowsky/hide-my-figma-cursor-extension)

\[35] Hide Cursor[ https://chromewebstore.google.com/detail/hide-cursor/kldbplphckmjnjljpfikobfpolmoidge/](https://chromewebstore.google.com/detail/hide-cursor/kldbplphckmjnjljpfikobfpolmoidge/)

\[36] Disable Mouse[ https://marketplace.visualstudio.com/items?itemName=yaamaa.disable-mouse](https://marketplace.visualstudio.com/items?itemName=yaamaa.disable-mouse)

\[37] Codebase Indexing[ https://docs.cursor.com/context/codebase-indexing](https://docs.cursor.com/context/codebase-indexing)

\[38] Cursor - Build Software Faster[ https://learn-cursor.com/en/docs/context/codebase-indexing](https://learn-cursor.com/en/docs/context/codebase-indexing)

\[39] Use Flow Knowledge Base in Cursor[ https://developers.flow.com/tutorials/ai-plus-flow/cursor](https://developers.flow.com/tutorials/ai-plus-flow/cursor)

\[40] Features[ https://cursordocs.com/en/docs/settings/ide/features](https://cursordocs.com/en/docs/settings/ide/features)

\[41] Temp Index for Cursor[ https://www.tek-tips.com/viewthread.cfm?qid=466867](https://www.tek-tips.com/viewthread.cfm?qid=466867)

\[42] Add Cursor editor rules and cursorignore functionality #29[ https://github.com/adrian-d-hidalgo/nestjs-mcp-server/issues/29](https://github.com/adrian-d-hidalgo/nestjs-mcp-server/issues/29)

\[43] cursor[ https://udn.realityripple.com/docs/Web/CSS/cursor](https://udn.realityripple.com/docs/Web/CSS/cursor)

\[44] DECLARE CURSOR statement[ https://www.ibm.com/docs/en/db2-big-sql/5.0.0?topic=statements-declare-cursor](https://www.ibm.com/docs/en/db2-big-sql/5.0.0?topic=statements-declare-cursor)

\[45] cursor[ https://www.codecademy.com/resources/docs/css/cursor](https://www.codecademy.com/resources/docs/css/cursor)

\[46] How to Ignore Sensitive Files in Cursor[ https://www.cursor.fan/tutorial/HowTo/how-to-ignore-sensitive-files-in-cursor/](https://www.cursor.fan/tutorial/HowTo/how-to-ignore-sensitive-files-in-cursor/)

\[47] Changing the look of Cursor using CSS[ https://www.tutorialspoint.com/changing-the-look-of-cursor-using-css](https://www.tutorialspoint.com/changing-the-look-of-cursor-using-css)

\[48] Git-related symbols not accessible through Chat #1989[ https://github.com/getcursor/cursor/issues/1989](https://github.com/getcursor/cursor/issues/1989)

\[49] Option to disable graph view and search indexing by default #170[ https://github.com/gitahead/gitahead/issues/170](https://github.com/gitahead/gitahead/issues/170)

\[50] Cursor Version Scanner | Cursor 版本扫描器[ https://github.com/veardk/cursor-version-scanner](https://github.com/veardk/cursor-version-scanner)

\[51] Go back tigers history overwrite and makes impossible to go forward #2981[ https://github.com/gitkraken/vscode-gitlens/issues/2981](https://github.com/gitkraken/vscode-gitlens/issues/2981)

\[52] cursor.maxTimeMS()[ https://www.mongodb.com/docs/manual/reference/method/cursor.maxTimeMS/](https://www.mongodb.com/docs/manual/reference/method/cursor.maxTimeMS/)

\[53] cursor.max()[ https://www.mongodb.com/docs/v5.0/reference/method/cursor.max/](https://www.mongodb.com/docs/v5.0/reference/method/cursor.max/)

\[54] MongoDB: cursor.maxTimeMS() method[ https://www.w3resource.com/mongodb/shell-methods/cursor/cursor-maxTimeMS.php](https://www.w3resource.com/mongodb/shell-methods/cursor/cursor-maxTimeMS.php)

\[55] cursor.maxAwaitTimeMS()[ https://www.mongodb.com/docs/manual/reference/method/cursor.maxAwaitTimeMS/](https://www.mongodb.com/docs/manual/reference/method/cursor.maxAwaitTimeMS/)

\[56] MongoDB sort memory limit exceeded when retrieving execution history #5947[ https://github.com/StackStorm/st2/issues/5947](https://github.com/StackStorm/st2/issues/5947)

\[57] cursor.limit() (mongosh method)[ https://www.mongodb.com/docs/manual/reference/method/cursor.limit/](https://www.mongodb.com/docs/manual/reference/method/cursor.limit/)

\[58] Disable/Remove "code" and "cursor" shortcuts[ https://forum.cursor.sh/t/disable-remove-code-and-cursor-shortcuts/1753](https://forum.cursor.sh/t/disable-remove-code-and-cursor-shortcuts/1753)

\[59] \[Feature Request]: command-line argument to disable extensions #12139[ https://github.com/AUTOMATIC1111/stable-diffusion-webui/issues/12139](https://github.com/AUTOMATIC1111/stable-diffusion-webui/issues/12139)

\[60] Windows Cursor Hiding Utility[ https://github.com/PRich57/windows-cursor-hiding-utility/](https://github.com/PRich57/windows-cursor-hiding-utility/)

\[61] Turn off extensions in "disable extensions" mode[ https://support.google.com/chrome/thread/170006083/turn-off-extensions-in-disable-extensions-mode?hl=en](https://support.google.com/chrome/thread/170006083/turn-off-extensions-in-disable-extensions-mode?hl=en)

\[62] Toggle Mouse Cursor[ https://github.com/vlazed/toggle-cursor](https://github.com/vlazed/toggle-cursor)

\[63] Disable or delay mouse pointer from detecting what is under it?[ https://answers.microsoft.com/en-us/windows/forum/all/disable-or-delay-mouse-pointer-from-detecting-what/31d5f894-6e96-403b-ac67-5e8ffbcb7bad](https://answers.microsoft.com/en-us/windows/forum/all/disable-or-delay-mouse-pointer-from-detecting-what/31d5f894-6e96-403b-ac67-5e8ffbcb7bad)

\[64] cursor[ https://github.com/GijsTimmers/cursor](https://github.com/GijsTimmers/cursor)

\[65] Cursor. Hide Method[ https://learn.microsoft.com/en-us/dotNet/API/system.windows.forms.cursor.hide?view=netframework-4.8.1](https://learn.microsoft.com/en-us/dotNet/API/system.windows.forms.cursor.hide?view=netframework-4.8.1)

\[66] How to ignore mouse cursor in image search[ https://www.autohotkey.com/boards/viewtopic.php?style=7\&t=93395](https://www.autohotkey.com/boards/viewtopic.php?style=7\&t=93395)

\[67] cursor 1.3.5[ https://pypi.org/project/cursor/](https://pypi.org/project/cursor/)

\[68] SlyyCooper/cursorrules-tools[ https://github.com/SlyyCooper/cursorrules-tools](https://github.com/SlyyCooper/cursorrules-tools)

\[69] Cursor[ https://pico-8.fandom.com/wiki/Cursor](https://pico-8.fandom.com/wiki/Cursor)

\[70] CURSOR—Set or Query the Cursor Position[ https://www.ibm.com/docs/en/zos/2.1.0?topic=statements-cursor-set-query-cursor-position](https://www.ibm.com/docs/en/zos/2.1.0?topic=statements-cursor-set-query-cursor-position)

\[71] Introducing The Cursor[ http://www.3kranger.com/HP3000/mpeix/en-mpe60/36216-90081/ch06s03.html](http://www.3kranger.com/HP3000/mpeix/en-mpe60/36216-90081/ch06s03.html)

\[72] MEMORY command[ https://www.ibm.com/docs/en/developer-for-zos/9.5.1?topic=commands-memory-command](https://www.ibm.com/docs/en/developer-for-zos/9.5.1?topic=commands-memory-command)

\[73] DECLARE CURSOR[ https://www.microfocus.com/documentation/server-express/sx40/rhsqlx2a.htm](https://www.microfocus.com/documentation/server-express/sx40/rhsqlx2a.htm)

\[74] CURSOR[ https://docs.opengauss.org/en/docs/3.0.0/docs/Developerguide/cursor.html](https://docs.opengauss.org/en/docs/3.0.0/docs/Developerguide/cursor.html)

\[75] Managing Extensions in Cursor[ https://www.cursor.fan/tutorial/HowTo/managing-extensions-in-cursor/](https://www.cursor.fan/tutorial/HowTo/managing-extensions-in-cursor/)

\[76] Disable or remove Add-ons[ https://support.mozilla.org/en-US/kb/disable-or-remove-add-ons#w\_removing-extensions](https://support.mozilla.org/en-US/kb/disable-or-remove-add-ons#w_removing-extensions)

\[77] Customization[ https://www.cursorless.org/docs/user/customization/](https://www.cursorless.org/docs/user/customization/)

\[78] Disable Extensions[ https://marketplace.visualstudio.com/items?itemName=muzaisimao.vscode-disable-extensions](https://marketplace.visualstudio.com/items?itemName=muzaisimao.vscode-disable-extensions)

\[79] Enabling and Disabling Extensions[ http://docs.oracle.com/en/cloud/saas/enterprise-performance-management-common/svsvs/enabling\_and\_disabling\_extensions.html](http://docs.oracle.com/en/cloud/saas/enterprise-performance-management-common/svsvs/enabling_and_disabling_extensions.html)

\[80] Increase your SQL Server performance by replacing cursors with set operations[ https://techcommunity.microsoft.com/t5/sql-server-blog/increase-your-sql-server-performance-by-replacing-cursors-with/ba-p/383359](https://techcommunity.microsoft.com/t5/sql-server-blog/increase-your-sql-server-performance-by-replacing-cursors-with/ba-p/383359)

\[81] Hide the cursor on a webpage using CSS and JavaScript[ https://www.tutorialspoint.com/hide-the-cursor-on-a-webpage-using-css-and-javascript](https://www.tutorialspoint.com/hide-the-cursor-on-a-webpage-using-css-and-javascript)

\[82] whitenoise/.cursorignore at master · parres-hq/whitenoise · GitHub[ https://github.com/parres-hq/whitenoise/blob/master/.cursorignore](https://github.com/parres-hq/whitenoise/blob/master/.cursorignore)

\[83] 40.7. Cursors[ https://www.postgresql.org/docs/9.5/plpgsql-cursors.html](https://www.postgresql.org/docs/9.5/plpgsql-cursors.html)

\[84] Insensitive cursor[ https://www.ibm.com/docs/en/db2/10.1.0?topic=features-insensitive-cursor](https://www.ibm.com/docs/en/db2/10.1.0?topic=features-insensitive-cursor)

\[85] Hide/disable git history located at the bottom of visual studio editor[ https://learn.microsoft.com/en-us/answers/questions/600002/hide-disable-git-history-located-at-the-bottom-of](https://learn.microsoft.com/en-us/answers/questions/600002/hide-disable-git-history-located-at-the-bottom-of)

\[86] CURSOR Command[ https://www.ibm.com/docs/en/zvse/6.2?topic=macros-cursor-command](https://www.ibm.com/docs/en/zvse/6.2?topic=macros-cursor-command)

\[87] 4 Moving the Cursor[ https://www.gnu.org/software/texinfo/manual/info-stnd/html\_node/Cursor-Commands.html](https://www.gnu.org/software/texinfo/manual/info-stnd/html_node/Cursor-Commands.html)

\[88] Ubuntu Manpage: DECLARE - define a cursor[ https://manpages.ubuntu.com/manpages/focal/man7/DECLARE.7.html](https://manpages.ubuntu.com/manpages/focal/man7/DECLARE.7.html)

\[89] Cursor-Related Commands[ https://docs.webmethods.io/on-premises/webmethods-tamino/en/9.5.0.1/ins/inoapi4c/api4c-cursorcmd.htm](https://docs.webmethods.io/on-premises/webmethods-tamino/en/9.5.0.1/ins/inoapi4c/api4c-cursorcmd.htm)

\[90] Create Cursor[ https://www.tedroche.com/hackfox7/s4g070.html](https://www.tedroche.com/hackfox7/s4g070.html)

\[91] .cursor command[ https://www3.rocketsoftware.com/rocketd3/support/documentation/d3nt/102/refman/Output\_Processor/dot\_cursor\_command.htm](https://www3.rocketsoftware.com/rocketd3/support/documentation/d3nt/102/refman/Output_Processor/dot_cursor_command.htm)

\[92] Shell Commands[ https://anysphere.mintlify.app/configuration/shell](https://anysphere.mintlify.app/configuration/shell)

\[93] vscodevim-disabler[ https://marketplace.visualstudio.com/items?itemName=tomasruizr.vscodevim-disabler](https://marketplace.visualstudio.com/items?itemName=tomasruizr.vscodevim-disabler)

\[94] Microsoft Edge - Extensions disable[ https://answers.microsoft.com/en-us/microsoftedge/forum/all/microsoft-edge-extensions-disable/562e9b18-c590-48fe-85a8-184bd72ce853](https://answers.microsoft.com/en-us/microsoftedge/forum/all/microsoft-edge-extensions-disable/562e9b18-c590-48fe-85a8-184bd72ce853)

\[95] 8 Fixes When the Text Cursor Turns White on Windows 11[ https://www.guidingtech.com/fix-text-cursor-turning-white-windows-11/](https://www.guidingtech.com/fix-text-cursor-turning-white-windows-11/)

\[96] Uninstall - Custom Cursor[ https://custom-cursor.com/fr/uninstall](https://custom-cursor.com/fr/uninstall)

\[97] @ Symbols do not work for code in node\_modules when .gitignore and .cursorignore do not exist[ https://forum.cursor.sh/t/symbols-do-not-work-for-code-in-node-modules-when-gitignore-and-cursorignore-do-not-exist/2799](https://forum.cursor.sh/t/symbols-do-not-work-for-code-in-node-modules-when-gitignore-and-cursorignore-do-not-exist/2799)

\[98] Quick Guide: Setting Up and Using Cursor with Claude 3.7[ https://cheesecakelabs.com/blog/using-cursor-and-claude/](https://cheesecakelabs.com/blog/using-cursor-and-claude/)

\[99] option to turn off history completely #32[ https://github.com/oracle/opengrok/issues/32](https://github.com/oracle/opengrok/issues/32)

\[100] About Cursor Configuration Parameter Values[ https://docs.oracle.com/cd/E05553\_01/books/ImpAppsDB2390/ImpAppsDB2390\_Maintenance8.html](https://docs.oracle.com/cd/E05553_01/books/ImpAppsDB2390/ImpAppsDB2390_Maintenance8.html)

\[101] #MaxMem[ https://www.autohotkey.com/docs/v1/lib/\_MaxMem.htm](https://www.autohotkey.com/docs/v1/lib/_MaxMem.htm)

\[102] Memory: Memory Available for Data Storage[ https://www.rdocumentation.org/packages/base/versions/3.6.2/topics/Memory](https://www.rdocumentation.org/packages/base/versions/3.6.2/topics/Memory)

\[103] memory.size: Report on Memory Allocation (on Windows)[ https://www.rdocumentation.org/packages/utils/versions/3.6.2/topics/memory.size](https://www.rdocumentation.org/packages/utils/versions/3.6.2/topics/memory.size)

\[104] Cap Memory of Process[ https://www.controlup.com/script-library-posts/cap-memory-of-process/](https://www.controlup.com/script-library-posts/cap-memory-of-process/)

\[105] Using vim from the terminal: too many command arguments #2002[ https://github.com/vim/vim/issues/2002](https://github.com/vim/vim/issues/2002)

\[106] Resource Parameters[ https://learn.microsoft.com/en-us/windows/win32/extensible-storage-engine/resource-parameters](https://learn.microsoft.com/en-us/windows/win32/extensible-storage-engine/resource-parameters)

\[107] GitHub - spotomato/DisableExtensions: Disable extensions upon closing ArcMap 10.4+[ https://github.com/spotomato/DisableExtensions](https://github.com/spotomato/DisableExtensions)

\[108] disable extensions not working for extension debugging #76989[ https://github.com/microsoft/vscode/issues/76989](https://github.com/microsoft/vscode/issues/76989)

\[109] Disable Microsoft Extensions to C++[ https://learn.microsoft.com/en-us/previous-versions/visualstudio/visual-studio-6.0/aa294019(v=vs.60)?redirectedfrom=MSDN](https://learn.microsoft.com/en-us/previous-versions/visualstudio/visual-studio-6.0/aa294019\(v=vs.60\)?redirectedfrom=MSDN)

\[110] Search, Install And Update GNOME Shell Extensions From The Command Line Using gnome-extensions-cli[ https://www.linuxuprising.com/2023/02/search-install-and-update-gnome-shell.html?m=1](https://www.linuxuprising.com/2023/02/search-install-and-update-gnome-shell.html?m=1)

\[111] Extensions Control - Visual Studio Marketplace[ https://marketplace.visualstudio.com/items?itemName=zardoy.extensions-control](https://marketplace.visualstudio.com/items?itemName=zardoy.extensions-control)

\[112] Enable Disable Chrome Extensions while Running[ https://groups.google.com/a/chromium.org/g/chromium-dev/c/9UOp5enlR84](https://groups.google.com/a/chromium.org/g/chromium-dev/c/9UOp5enlR84)

\[113] @vogelweb/cursor-js - npm[ https://www.npmjs.com/package/@vogelweb/cursor-js](https://www.npmjs.com/package/@vogelweb/cursor-js)

\[114] wizarr/.cursorignore at main · wizarrrr/wizarr · GitHub[ https://github.com/wizarrrr/wizarr/blob/main/.cursorignore](https://github.com/wizarrrr/wizarr/blob/main/.cursorignore)

\[115] How exactly can I ignore folders like node\_modules in telescope? \[SOLVED] #522[ https://github.com/nvim-telescope/telescope.nvim/issues/522](https://github.com/nvim-telescope/telescope.nvim/issues/522)

\[116] pyscf parses sys.argv when setting max\_memory on a Mole object #470[ https://github.com/pyscf/pyscf/issues/470](https://github.com/pyscf/pyscf/issues/470)

\[117] Command prompt (Cmd. exe) command-line string limitation[ https://learn.microsoft.com/en-us/troubleshoot/windows-client/shell-experience/command-line-string-limitation](https://learn.microsoft.com/en-us/troubleshoot/windows-client/shell-experience/command-line-string-limitation)

\[118] command line parameter limit[ https://coderanch.com/t/630844/java/command-line-parameter-limit](https://coderanch.com/t/630844/java/command-line-parameter-limit)

\[119] cursor.max()[ https://www.mongodb.com/docs/v4.4/reference/method/cursor.max/](https://www.mongodb.com/docs/v4.4/reference/method/cursor.max/)

\[120] cursor.max() (mongosh method)[ https://www.mongodb.com/docs/v6.0/reference/method/cursor.max/](https://www.mongodb.com/docs/v6.0/reference/method/cursor.max/)

\[121] Administrator's Guide[ https://docs.oracle.com/en/applications/enterprise-performance-management/11.2/hfmam/open\_cursors.html](https://docs.oracle.com/en/applications/enterprise-performance-management/11.2/hfmam/open_cursors.html)

\[122] Disable and re-enable only my Enabled extensions  #100834[ https://github.com/microsoft/vscode/issues/100834](https://github.com/microsoft/vscode/issues/100834)

\[123] Command Line --list-extensions[ https://forum.cursor.sh/t/command-line-list-extensions/103565](https://forum.cursor.sh/t/command-line-list-extensions/103565)

\[124] .gitignore for node\_modules? #25[ https://github.com/harthur/brain/issues/25](https://github.com/harthur/brain/issues/25)

\[125] Ignore Node dependencies[ https://thelinuxcode.com/ignore-node-dependencies/](https://thelinuxcode.com/ignore-node-dependencies/)

\[126] Build error while importing cursor #1795[ https://github.com/immutable-js/immutable-js/issues/1795](https://github.com/immutable-js/immutable-js/issues/1795)

\[127] More Actions ... does not have Disable All Extensions / Enable All Extensions #121596[ https://github.com/microsoft/vscode/issues/121596](https://github.com/microsoft/vscode/issues/121596)

\[128] cursor.max() — MongoDB Manual[ https://www.mongodb.com/docs/v6.0/reference/method/cursor.max/#mongodb-method-cursor.max](https://www.mongodb.com/docs/v6.0/reference/method/cursor.max/#mongodb-method-cursor.max)

\[129] Change the Size of the Pointer Block Cache[ https://docs.vmware.com/en/VMware-vSphere/7.0/com.vmware.vsphere.storage.doc/GUID-8B3D9E90-C477-4815-B216-0D40B552BE6B.html](https://docs.vmware.com/en/VMware-vSphere/7.0/com.vmware.vsphere.storage.doc/GUID-8B3D9E90-C477-4815-B216-0D40B552BE6B.html)

\[130] Introduction to MongoDB cursor.max() Method[ https://www.sqliz.com/mongodb-ref/cursor-max/](https://www.sqliz.com/mongodb-ref/cursor-max/)

\[131] cursor.limit()[ https://www.mongodb.com/docs/v2.6/reference/method/cursor.limit/](https://www.mongodb.com/docs/v2.6/reference/method/cursor.limit/)

\[132] 5. Memory Commands[ https://docs.rtems.org/docs/main/shell/memory\_commands.html](https://docs.rtems.org/docs/main/shell/memory_commands.html)

\[133] Introduction to MongoDB cursor.maxawaittimems() Method[ https://www.sqliz.com/mongodb-ref/cursor-maxawaittimems/](https://www.sqliz.com/mongodb-ref/cursor-maxawaittimems/)

\[134] Troubleshooting ORA-01000: maximum open cursors exceeded[ https://www.ibm.com/support/pages/troubleshooting-ora-01000-maximum-open-cursors-exceeded](https://www.ibm.com/support/pages/troubleshooting-ora-01000-maximum-open-cursors-exceeded)

\[135] Exclude node\_modules folder with .gitignore file[ https://sebhastian.com/git-ignore-node\_modules/](https://sebhastian.com/git-ignore-node_modules/)

\[136] Testing AI coding agents (2025): Cursor vs. Claude, OpenAI, and Gemini[ https://render.com/blog/ai-coding-agents-benchmark](https://render.com/blog/ai-coding-agents-benchmark)

\[137] Update .gitignore to ignore node\_modules directory #659[ https://github.com/publiclab/spectral-workbench/issues/659](https://github.com/publiclab/spectral-workbench/issues/659)

\[138] Version 0.45.8 \[Windows] - Crash with JS error #2657[ https://github.com/getcursor/cursor/issues/2657](https://github.com/getcursor/cursor/issues/2657)

\[139] add a glob example for how to ignore node\_modules #1358[ https://github.com/prettier/prettier/issues/1358](https://github.com/prettier/prettier/issues/1358)

\[140] How to Change Cursor Size on Debian 10 Desktop[ https://vitux.com/how-to-change-cursor-size-on-debian-10-desktop/amp/](https://vitux.com/how-to-change-cursor-size-on-debian-10-desktop/amp/)

\[141] d, da, db, dc, dd, dD, df, dp, dq, du, dw (Display Memory)[ https://learn.microsoft.com/en-us/windows-hardware/drivers/debuggercmds/d--da--db--dc--dd--dd--df--dp--dq--du--dw--dw--dyb--dyd--display-memor](https://learn.microsoft.com/en-us/windows-hardware/drivers/debuggercmds/d--da--db--dc--dd--dd--df--dp--dq--du--dw--dw--dyb--dyd--display-memor)

\[142] Tuning Virtual Memory Manager (VMM)[ https://docs.oracle.com/en/database/oracle/oracle-database/19/cwaix/tuning-virtual-memory-manager.html](https://docs.oracle.com/en/database/oracle/oracle-database/19/cwaix/tuning-virtual-memory-manager.html)

\[143] Use After and Before Cursor Properly in a Front-End App[ https://forums.fauna.com/t/use-after-and-before-cursor-properly-in-a-front-end-app/1606](https://forums.fauna.com/t/use-after-and-before-cursor-properly-in-a-front-end-app/1606)

\[144] Custom mouse cursor with CSS[ https://nikitahl.com/custom-mouse-cursor](https://nikitahl.com/custom-mouse-cursor)

\[145] Insensitive cursor[ https://www.ibm.com/docs/en/db2/9.7?topic=features-insensitive-cursor](https://www.ibm.com/docs/en/db2/9.7?topic=features-insensitive-cursor)

\[146] Cursor Commander[ https://marketplace.visualstudio.com/items?itemName=skafau.cursor-commander](https://marketplace.visualstudio.com/items?itemName=skafau.cursor-commander)

\[147] Using dynamic help[ https://learn.microsoft.com/en-us/powershell/scripting/learn/shell/dynamic-help?view=powershell-7.3](https://learn.microsoft.com/en-us/powershell/scripting/learn/shell/dynamic-help?view=powershell-7.3)

\[148] Introducing The Cursor[ http://www.3kranger.com/HP3000/mpeix/en-mpe60/36217-90161/ch06s03.html](http://www.3kranger.com/HP3000/mpeix/en-mpe60/36217-90161/ch06s03.html)

\[149] How to get coordinates of first displayed line in windows relatively to DATA-BUFFER ? #19216[ https://github.com/microsoft/terminal/issues/19216](https://github.com/microsoft/terminal/issues/19216)

\[150] Using commands that are sensitive to the cursor position[ https://www.ibm.com/docs/en/developer-for-zos/9.1.1?topic=panel-using-commands-that-are-sensitive-cursor-position](https://www.ibm.com/docs/en/developer-for-zos/9.1.1?topic=panel-using-commands-that-are-sensitive-cursor-position)

\[151] CURSOR command (full-screen mode)[ https://www.ibm.com/docs/en/debug-for-zos/16.0?topic=commands-cursor-command-full-screen-mode](https://www.ibm.com/docs/en/debug-for-zos/16.0?topic=commands-cursor-command-full-screen-mode)

\[152] Term::Cursor[ https://github.com/crystal-term/cursor](https://github.com/crystal-term/cursor)

\[153] Safari 17 Disable Extensions[ https://discussions.apple.com/thread/255159834](https://discussions.apple.com/thread/255159834)

\[154] Disabling and Removing Extensions[ https://archive.flossmanuals.net/chromium/ch055\_disabling-and-removing-extensions.html](https://archive.flossmanuals.net/chromium/ch055_disabling-and-removing-extensions.html)

\[155] Add option Enable/Disable extension from the Puzzle Button (≡)[ https://community.brave.com/t/add-option-enable-disable-extension-from-the-puzzle-button/502267](https://community.brave.com/t/add-option-enable-disable-extension-from-the-puzzle-button/502267)

\[156] Disable Extensions on Macintosh[ https://encoresupport.freshdesk.com/support/solutions/articles/43000029529-disable-extensions-on-macintosh](https://encoresupport.freshdesk.com/support/solutions/articles/43000029529-disable-extensions-on-macintosh)

\[157] Can't toggle "Disable Extensions" in Develop menu in Safari 11.1[ https://discussions.apple.com/thread/8355867](https://discussions.apple.com/thread/8355867)

\[158] DAEDAL/.gitignore at main · Li-Jinsong/DAEDAL · GitHub[ https://github.com/Li-Jinsong/DAEDAL/blob/main/.gitignore](https://github.com/Li-Jinsong/DAEDAL/blob/main/.gitignore)

\[159] agentic-cursorrules[ https://github.com/s-smits/agentic-cursorrules](https://github.com/s-smits/agentic-cursorrules)

\[160] Move cursor up or down a few lines at a time from the keyboard[ https://forum.sublimetext.com/t/move-cursor-up-or-down-a-few-lines-at-a-time-from-the-keyboard/28915](https://forum.sublimetext.com/t/move-cursor-up-or-down-a-few-lines-at-a-time-from-the-keyboard/28915)

\[161] Cursor in Command Area[ https://ftpdocs.broadcom.com/cadocs/0/CA%20Ideal%2014%200-ENU/Bookshelf\_Files/HTML/ID1402\_Working\_in\_the\_Environment\_ENU/876860.html](https://ftpdocs.broadcom.com/cadocs/0/CA%20Ideal%2014%200-ENU/Bookshelf_Files/HTML/ID1402_Working_in_the_Environment_ENU/876860.html)

\[162] transactional-tests: generalized cursor support #23129[ https://github.com/MystenLabs/sui/pull/23129](https://github.com/MystenLabs/sui/pull/23129)

\[163] Command Line Arguments in C++[ https://www.geeksforgeeks.org/command-line-arguments-in-cpp/](https://www.geeksforgeeks.org/command-line-arguments-in-cpp/)

\[164] Built-in Commands[ https://code.visualstudio.com/api/references/commands](https://code.visualstudio.com/api/references/commands)

\[165] How to: Disable and Re-enable Visual Studio Tools and Extensions[ https://learn.microsoft.com/en-us/previous-versions/visualstudio/visual-studio-2010/dd293640(v=vs.100)](https://learn.microsoft.com/en-us/previous-versions/visualstudio/visual-studio-2010/dd293640\(v=vs.100\))

\[166] react-cool-cursors[ https://www.npmjs.com/package/react-cool-cursors](https://www.npmjs.com/package/react-cool-cursors)

\[167] How to Git Ignore Node Modules Folder Globally[ https://www.squash.io/how-to-git-ignore-node-modules-folder-everywhere/](https://www.squash.io/how-to-git-ignore-node-modules-folder-everywhere/)

\[168] @louislam/dropbox-ignore-node\_modules[ https://www.npmjs.com/package/@louislam/dropbox-ignore-node\_modules](https://www.npmjs.com/package/@louislam/dropbox-ignore-node_modules)

\[169] Ignore Node\_modules Folder Everywhere[ https://www.delftstack.com/howto/git/git-ignore-node\_modules/](https://www.delftstack.com/howto/git/git-ignore-node_modules/)

\[170] User and workspace settings[ https://code.visualstudio.com/docs/getstarted/settings](https://code.visualstudio.com/docs/getstarted/settings)

\[171] cursor . not working with git-bash #1121[ https://github.com/cursor/cursor/issues/1121](https://github.com/cursor/cursor/issues/1121)

\[172] Git - git-diff Documentation[ https://git-scm.com/docs/git-diff](https://git-scm.com/docs/git-diff)

\[173] PM2 — Start Node.js App With Increased Memory Limit From Command Line[ https://futurestud.io/tutorials/pm2-start-node-js-app-with-increased-memory-limit-from-command-line](https://futurestud.io/tutorials/pm2-start-node-js-app-with-increased-memory-limit-from-command-line)

\[174] cursor.max()[ https://www.mongodb.com/docs/v2.4/reference/method/cursor.max/](https://www.mongodb.com/docs/v2.4/reference/method/cursor.max/)

\[175] cursor.max()[ https://www.mongodb.com/docs/v5.1/reference/method/cursor.max/](https://www.mongodb.com/docs/v5.1/reference/method/cursor.max/)

\[176] SlyyCooper/cursorrules-architect[ https://github.com/SlyyCooper/cursorrules-architect](https://github.com/SlyyCooper/cursorrules-architect)

\[177] Git - hash-function-transition Documentation[ https://git-scm.com/docs/hash-function-transition/](https://git-scm.com/docs/hash-function-transition/)

\[178] stop Indexing please!!!!!!\![ https://intellij-support.jetbrains.com/hc/en-us/community/posts/207671405-stop-Indexing-please](https://intellij-support.jetbrains.com/hc/en-us/community/posts/207671405-stop-Indexing-please)

\[179] Change Extensions settings on Mac[ https://support.apple.com/en-euro/guide/mac-help/mchl8baf92fe/mac](https://support.apple.com/en-euro/guide/mac-help/mchl8baf92fe/mac)

\[180] History tab[ https://www.jetbrains.com/help/pycharm/version-control-tool-window-history-tab.html](https://www.jetbrains.com/help/pycharm/version-control-tool-window-history-tab.html)

\[181] How I use Cursor (+ my best tips)[ https://www.builder.io/blog/cursor-tips](https://www.builder.io/blog/cursor-tips)

\[182] Using command-line arguments[ https://www.ibm.com/docs/en/cobol-linux-x86/1.2?topic=data-using-command-line-arguments](https://www.ibm.com/docs/en/cobol-linux-x86/1.2?topic=data-using-command-line-arguments)

\[183] Git ignore node modules (.gitignore for node\_modules)[ https://flatirons.com/blog/understanding-and-implementing-gitignore-for-node\_modules/](https://flatirons.com/blog/understanding-and-implementing-gitignore-for-node_modules/)

\[184] User and Workspace Settings[ https://code.visualstudio.com/docs/customization/userandworkspace](https://code.visualstudio.com/docs/customization/userandworkspace)

\[185] Editor Settings[ https://spckio.github.io/spck-documentation/editor-settings.html](https://spckio.github.io/spck-documentation/editor-settings.html)

\[186] Settings[ https://gitextensions.readthedocs.io/en/latest/settings/](https://gitextensions.readthedocs.io/en/latest/settings/)

> （注：文档部分内容可能由 AI 生成）