@echo off
REM 紧急修复Cursor内存问题 - 基于4.3GB内存使用情况
REM 立即执行，防止系统崩溃

echo ========================================
echo 🚨 紧急修复Cursor内存问题
echo 当前内存使用: 4.3GB (危险级别)
echo ========================================

echo [紧急步骤1] 立即停止所有Cursor进程...
taskkill /f /im cursor.exe
taskkill /f /im "Cursor.exe"
taskkill /f /im "cursor-*"
echo 等待进程完全停止...
timeout /t 5 >nul

echo [紧急步骤2] 强制清理内存缓存...
if exist "%APPDATA%\Cursor\Cache" (
    echo 清理主缓存目录...
    rmdir /s /q "%APPDATA%\Cursor\Cache"
)
if exist "%APPDATA%\Cursor\CachedData" (
    echo 清理缓存数据...
    rmdir /s /q "%APPDATA%\Cursor\CachedData"
)
if exist "%APPDATA%\Cursor\logs" (
    echo 清理日志文件...
    del /q "%APPDATA%\Cursor\logs\*.log"
)
if exist "%APPDATA%\Cursor\index" (
    echo 清理索引文件...
    rmdir /s /q "%APPDATA%\Cursor\index"
)

echo [紧急步骤3] 清理系统临时文件...
del /q "%TEMP%\cursor*" 2>nul
del /q "%TEMP%\vscode*" 2>nul

echo [紧急步骤4] 设置严格内存限制...
set NODE_OPTIONS=--max-old-space-size=2048
set ELECTRON_HEAP_SIZE=2048

echo [紧急步骤5] 使用最严格限制重启Cursor...
echo 启动参数: --max-memory=2048 --disable-gpu --no-sandbox --disable-dev-shm-usage

where cursor >nul 2>&1
if %errorlevel% neq 0 (
    echo 尝试使用完整路径启动...
    if exist "%LOCALAPPDATA%\Programs\cursor\Cursor.exe" (
        start "" "%LOCALAPPDATA%\Programs\cursor\Cursor.exe" --max-memory=2048 --disable-gpu --no-sandbox --disable-dev-shm-usage
    ) else (
        echo 请手动启动Cursor并使用以下参数:
        echo cursor --max-memory=2048 --disable-gpu --no-sandbox --disable-dev-shm-usage
    )
) else (
    cursor --max-memory=2048 --disable-gpu --no-sandbox --disable-dev-shm-usage
)

echo ========================================
echo ✅ 紧急修复完成！
echo 内存限制已设置为2GB
echo 建议立即启动内存监控
echo ========================================

echo 是否启动内存监控? (Y/N)
set /p monitor="请输入: "
if /i "%monitor%"=="Y" (
    echo 启动内存监控...
    powershell -ExecutionPolicy Bypass -File "scripts\cursor_memory_monitor.ps1" -ThresholdMB 1800
)

pause
